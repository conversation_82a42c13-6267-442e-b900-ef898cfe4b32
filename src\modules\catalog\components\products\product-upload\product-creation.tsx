import WarnInput from "@/components/input/warn-input";
import Text from "@/styles/text-styles";
import FormSubmission from "../../form-submission";
import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";
import { DashboardListsContainerSkeleton } from "@/components/dashbord-lists-container";
import { Skeleton } from "@/components/ui/skeleton";
import { Label } from "@/components/ui/label";
// import useBrands from "@/modules/catalog/hooks/brands/use-brands";
import { useState } from "react";
import useSeoMetaData from "@/modules/seo/hooks/use-seo-meta-data";
<<<<<<< HEAD
import SeoMetaContent from "../../../../seo/components/seo-meta-content/meta-content";
import KeywordsSection from "@/modules/seo/components/seo-meta-content/keywords";
// Commented out for now - can be used in the future
// import useCategories from "@/modules/catalog/hooks/categories/use-categories";
// import CategoriesSelection from "../../categories/categories-selection";
import { disableScrollOnNumberInput } from "@/utils/number-input";
import { Textarea } from "@/components/ui/textarea";
import DynamicArrayManager from "../../dynamic-array-manager";
import SectionItem from "../../section-item";
import FaqItem from "../../faq-item";
import { FaqType, SectionType } from "@/modules/catalog/types/products";
import CategoriesSelection from "../../categories/categories-selection";
import useCategories from "@/modules/catalog/hooks/categories/use-categories";

export default function ProductCreation() {
  const {
    metaTitle,
    metaDescription,
    keywords,
    handleMetaTitleChange,
    handleMetaDescriptionChange,
    addNewKeyword,
    removeKeyword,
    getMetaContent,
  } = useSeoMetaData();
  const { formRef, submitProduct, warning } = useProductUpload(
    getMetaContent()
  );

  // Commented out for now - can be used in the future
=======
import MultilanguageSeoContent from "@/modules/seo/components/seo-meta-content/multilanguage-seo-content";
import useCategories from "@/modules/catalog/hooks/categories/use-categories";
import CategoriesSelection from "../../categories/categories-selection";
import { disableScrollOnNumberInput } from "@/utils/number-input";
import LanguageTabs from "../../brands/brand-upload/language-tabs";
import MultilanguageProductFormFields from "./multilanguage-product-form-fields";
import { usePreviousUrl } from "@/hooks/urls-management/use-previous-url";
import { Language } from "@/modules/seo/types/multilanguage-seo";
import useProductCreation from "@/modules/catalog/hooks/products/use-product-creation";

export default function ProductCreation() {
>>>>>>> 1a01b64fcd4886e9063b75aa7f3a125119f2a87c
  const { categories, categoriesAreLoading } = useCategories();
  // const { brands, brandsAreLoading } = useBrands({});
  const [selectedCategoriesIds, setSelectedCategoriesIds] = useState<
    { id: string; slug: string }[]
  >([]);

<<<<<<< HEAD
  // const [selectedbrand, setSelectedbrand] = useState("");
  const [sections, setSections] = useState<SectionType[]>([]);
  const [faqs, setFaqs] = useState<FaqType[]>([]);
=======
  const [selectedbrand, setSelectedbrand] = useState("");
  const [activeLanguage, setActiveLanguage] = useState("french");

  const {
    metaContent,
    handleMetaTitleChange,
    handleMetaDescriptionChange,
    addNewKeyword,
    removeKeyword,
    getMultilanguageMetaContent,
  } = useSeoMetaData(activeLanguage as Language);
>>>>>>> 1a01b64fcd4886e9063b75aa7f3a125119f2a87c

  const router = useRouter();
  const previousUrl = usePreviousUrl();
  const uploadContent = useTranslations("shared.forms.upload");
  const t = useTranslations("ProductsManagement");

<<<<<<< HEAD
  // Commented out for now - can be used in the future
  // return !(brandsAreLoading || brands === undefined) &&

  return !(categoriesAreLoading || categories === undefined) && true ? (
=======
  const { formRef, submitProduct, warning, isPending, handleLanguageChange } =
    useProductCreation(getMultilanguageMetaContent);

  const cancelSubmission = () => {
    if (previousUrl && previousUrl.startsWith("/products"))
      router.push(previousUrl);
    else router.push("/products");
  };

  return !(brandsAreLoading || brands === undefined) &&
    !(categoriesAreLoading || categories === undefined) ? (
>>>>>>> 1a01b64fcd4886e9063b75aa7f3a125119f2a87c
    <div className="flex flex-col py-3">
      {
        <FormSubmission
          cancel={uploadContent("cancel")}
          submit={uploadContent("save")}
          isPending={isPending}
          onCancel={cancelSubmission}
          onSubmit={submitProduct}
        >
          <form ref={formRef} className="flex flex-col extraXL:flex-row gap-4">
            <div className="basis-[70%] regularL:order-1 order-2 flex flex-col gap-5">
              {/* Language Content Card */}
              <div className="rounded-2xl border border-lightGray p-7 bg-white space-y-7">
                <LanguageTabs
                  options={[
                    { key: "arabic", value: t("languages.arabic") },
                    { key: "french", value: t("languages.french") },
                    { key: "english", value: t("languages.english") },
                  ]}
                  onSelect={(language) => {
                    setActiveLanguage(language);
                  }}
                  selectedValue={activeLanguage}
                />
                <Text textStyle="TS4" className="font-bold text-black">
                  {t("productContent")}
                </Text>
                <div className="text-red self-center">{warning}</div>

                <div className="w-full">
                  <div
                    style={{
                      display: activeLanguage === "arabic" ? "block" : "none",
                    }}
                  >
                    <MultilanguageProductFormFields
                      multilanguage={true}
                      language="arabic"
                    />
                  </div>

                  <div
                    style={{
                      display: activeLanguage === "french" ? "block" : "none",
                    }}
                  >
                    <MultilanguageProductFormFields
                      multilanguage={true}
                      language="french"
                    />
                  </div>

                  <div
                    style={{
                      display: activeLanguage === "english" ? "block" : "none",
                    }}
                  >
                    <MultilanguageProductFormFields
                      multilanguage={true}
                      language="english"
                    />
                  </div>
                </div>
              </div>

              {/* Product Settings Card */}
              <div className=" rounded-2xl border border-lightGray p-7 bg-white  space-y-7">
                <Text textStyle="TS5" className="font-bold text-black">
                  {t("productSettings")}
                </Text>

                {/* Commented out for now - can be used in the future */}
                {/* Brand */}
                {/* <div>
                  <div className="w-full flex flex-col space-y-3">
                    <Label htmlFor="brandId">
                      {`${uploadContent(
                        "brandLabels.brandType"
                      )} ${uploadContent("optional")}`}
                    </Label>
                    <ListPicker
                      data={brands}
                      onChange={(id) => {
                        setSelectedbrand(id);
                      }}
                      selectedElementId={selectedbrand}
                      className="w-full max-w-full"
                      dropdownClassName="L:w-[300px] shadow-md border"
                      pickedElementName={"brandId"}
                    />
                  </div>
                </div> */}

                <div className="w-full flex flex-col space-y-3">
                  <Label htmlFor="displayOrder">
                    {uploadContent("productLabels.displayOrder")}{" "}
                    {uploadContent("optional")}
                  </Label>
                  <WarnInput
                    name="displayOrder"
                    type="number"
                    onWheel={disableScrollOnNumberInput}
                    warning=""
                    placeholder={uploadContent("productLabels.displayOrder")}
                  />
                </div>

                <CategoriesSelection
                  setSelectedCategories={setSelectedCategoriesIds}
                  className="flex flex-col 2L:flex-col gap-5 L:gap-6 mt-6"
                />
                {selectedCategoriesIds.length > 0 ? (
                  selectedCategoriesIds.map((cat) => (
                    <input
                      key={cat.id}
                      name={"categoryIds"}
                      className="hidden"
                      value={cat.id}
                      readOnly
                    />
                  ))
                ) : (
                  <input
                    name={"categoryIds"}
                    className="hidden"
                    value={""}
                    readOnly
                  />
                )}

                <DynamicArrayManager
                  title={uploadContent("productLabels.sections")}
                  items={sections}
                  onItemsChange={setSections}
                  renderItem={(section, index, onUpdate, onRemove) => (
                    <SectionItem
                      key={index}
                      section={section}
                      index={index}
                      onUpdate={onUpdate}
                      onRemove={onRemove}
                    />
                  )}
                  createNewItem={() => ({
                    title: "",
                    description: "",
                    displayOrder: sections.length + 1,
                  })}
                  className="mt-6"
                />

                <DynamicArrayManager
                  title={uploadContent("productLabels.faqs")}
                  items={faqs}
                  onItemsChange={setFaqs}
                  renderItem={(faq, index, onUpdate, onRemove) => (
                    <FaqItem
                      key={index}
                      faq={faq}
                      index={index}
                      onUpdate={onUpdate}
                      onRemove={onRemove}
                    />
                  )}
                  createNewItem={() => ({
                    question: "",
                    answer: "",
                    displayOrder: faqs.length + 1,
                  })}
                  className="mt-6"
                />
              </div>
            </div>
            <div className="basis-[30%] order-2 space-y-4">
              <div className="rounded-2xl border border-lightGray p-7 bg-white space-y-5">
                <MultilanguageSeoContent
                  metaContent={metaContent}
                  activeLanguage={activeLanguage as Language}
                  changeMetaTitle={handleMetaTitleChange}
                  changeMetaDescription={handleMetaDescriptionChange}
                  addNewKeyword={addNewKeyword}
                  removeKeyword={removeKeyword}
                />
              </div>
            </div>
          </form>
        </FormSubmission>
      }
    </div>
  ) : (
    <div className="py-2 w-full flex XL:flex-row flex-col XL:gap-8 gap-4">
      <DashboardListsContainerSkeleton className="flex-1">
        {/* Product Information Section */}
        <div className="w-full flex flex-col space-y-4">
          <div className="w-full flex flex-col space-y-1">
            <Skeleton className="h-6 w-32" /> {/* Code à barre */}
            <Skeleton className="h-9 w-full max-w-[500px]" />
          </div>
          <div className="w-full flex flex-col space-y-1">
            <Skeleton className="h-6 w-32" /> {/* Nom de produit */}
            <Skeleton className="h-9 w-full max-w-[500px]" />
          </div>
          <div className="w-full flex flex-col space-y-1">
            <Skeleton className="h-6 w-32" /> {/* catégorie mère */}
            <div className="flex gap-4">
              <Skeleton className="h-9 w-1/2" /> {/* Select product category */}
              <Skeleton className="h-9 w-1/2" /> {/* Select product category */}
            </div>
          </div>
          <div className="w-full flex flex-col space-y-1">
            <Skeleton className="h-6 w-32" /> {/* Description */}
            <Skeleton className="h-24 w-full max-w-[800px]" />
          </div>
          <div className="w-full flex flex-col space-y-1">
            <Skeleton className="h-6 w-32" /> {/* Quantity */}
            <Skeleton className="h-9 w-full max-w-[500px]" />
          </div>

          <div className="w-full flex flex-col space-y-1">
            <Skeleton className="h-6 w-32" /> {/* Product variations */}
            <div className="flex items-center gap-4">
              <Skeleton className="h-10 w-20" />{" "}
              {/* Display variations toggle */}
              <Skeleton className="h-10 w-24" /> {/* Delete Item button */}
              <Skeleton className="h-10 w-20" /> {/* Add Item button */}
            </div>
            <div className="grid grid-cols-2 gap-4 mt-4">
              <div className="flex flex-col items-center">
                <Skeleton className="h-32 w-full" />{" "}
                {/* Product variation image */}
                <Skeleton className="h-6 w-32 mt-2" /> {/* Variation name */}
              </div>
              <div className="flex flex-col items-center">
                <Skeleton className="h-32 w-full" />{" "}
                {/* Product variation image */}
                <Skeleton className="h-6 w-32 mt-2" /> {/* Variation name */}
              </div>
              <div className="flex flex-col items-center">
                <Skeleton className="h-32 w-full" />{" "}
                {/* Product variation image */}
                <Skeleton className="h-6 w-32 mt-2" /> {/* Variation name */}
              </div>
              <div className="flex flex-col items-center">
                <Skeleton className="h-32 w-full" />{" "}
                {/* Product variation image */}
                <Skeleton className="h-6 w-32 mt-2" /> {/* Variation name */}
              </div>
            </div>
          </div>
        </div>
      </DashboardListsContainerSkeleton>

      <DashboardListsContainerSkeleton>
        {/* Keywords & SEO Settings Section */}
        <div className="w-full flex flex-col space-y-4">
          <div className="w-full flex flex-col space-y-1">
            <Skeleton className="h-6 w-32" /> {/* Keywords */}
            <Skeleton className="h-9 w-full" /> {/* Add Keywords input */}
            <div className="flex gap-2 mt-2">
              <Skeleton className="h-8 w-24" /> {/* Tag chip */}
              <Skeleton className="h-8 w-24" /> {/* Tag chip */}
              <Skeleton className="h-8 w-32" /> {/* Tag chip */}
            </div>
          </div>
          <div className="w-full flex flex-col space-y-1">
            <Skeleton className="h-6 w-32" /> {/* SEO Settings */}
            <div className="w-full flex flex-col space-y-1">
              <Skeleton className="h-6 w-24" /> {/* Meta-Title */}
              <Skeleton className="h-9 w-full" />
            </div>
            <div className="w-full flex flex-col space-y-1">
              <Skeleton className="h-6 w-32" /> {/* Meta-Description */}
              <Skeleton className="h-9 w-full" />
            </div>
          </div>
        </div>
      </DashboardListsContainerSkeleton>
    </div>
  );
}
