import { CustomError } from "@/utils/custom-error";

export function validateItemData(
  formData: FormData,
  isEdition: boolean = false
): void {
  // For creation, productId is required. For edition, it's not needed since item already exists
  const requiredFields: string[] = isEdition
    ? ["barcode", "quantity", "price"]
    : ["productId", "barcode", "quantity", "price"];

  const missingFields: string[] = [];

  // Check required fields
  requiredFields.forEach((field) => {
    if (!formData.has(field) || formData.get(field) === "") {
      missingFields.push(field);
    }
  });

  if (missingFields.length > 0) {
    throw new CustomError(`Invalid Data!`, 400, "P1000");
  }

  // Validate image requirements (at least one image is needed)
  const image = formData.get("image");
  const defaultImage = formData.get("defaultImage");
  const hasImages = formData
    .getAll("images")
    .some((img) => img instanceof File && img.size > 0);

  if (
    (!defaultImage || defaultImage === "") && // no default image
    (!image || (image instanceof File && image.size === 0)) && // no main image
    !hasImages // no additional images
  ) {
<<<<<<< HEAD
    throw new CustomError("Invalid Data!", 400, "P1000");
  } else if (image instanceof File && image.size === 0) {
    // remove empty image file if there's a default image
=======
    throw new CustomError("Missed Data!", 400);
  } else if (image instanceof File && image.size === 0)
>>>>>>> 1a01b64fcd4886e9063b75aa7f3a125119f2a87c
    formData.delete("image");
  }

  formData.delete("defaultImage");

  // Validate price (must be greater than 0)
  const price = formData.get("price");
  if (price !== null && typeof price === "string") {
<<<<<<< HEAD
    const priceValue = parseFloat(price);
    if (isNaN(priceValue) || priceValue <= 0) {
      throw new CustomError(`Invalid Data!`, 400, "P1000");
=======
    if (parseFloat(price) < 0) {
      throw new CustomError(`Invalid Data!`, 400);
>>>>>>> 1a01b64fcd4886e9063b75aa7f3a125119f2a87c
    }
  }

  // Validate quantity (must be greater than 0)
  const quantity = formData.get("quantity");
  if (quantity !== null && typeof quantity === "string") {
    const quantityValue = parseFloat(quantity);
    if (isNaN(quantityValue) || quantityValue <= 0) {
      throw new CustomError(`Invalid Data!`, 400, "P1000");
    }
  }

  // Validate productId (must be a valid string)
  const productId = formData.get("productId");
  if (productId !== null && typeof productId === "string") {
    if (productId.trim().length === 0) {
      throw new CustomError(`Invalid Data!`, 400, "P1000");
    }
  }
}
