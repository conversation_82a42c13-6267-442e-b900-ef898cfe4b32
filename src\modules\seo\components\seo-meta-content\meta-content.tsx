import { useTranslations } from "next-intl";
import Text from "@/styles/text-styles";
import MultilanguageMetaFields from "./multilanguage-meta-fields";
import { Language } from "../../types/multilanguage-seo";

interface SeoSettingsProps {
  metaTitle: string;
  metaDescription: string;
  activeLanguage: Language;
  changeMetaTitle: (title: string) => void;
  changeMetaDescription: (description: string) => void;
}

export default function SeoMetaContent({
  metaTitle,
  metaDescription,
  activeLanguage,
  changeMetaTitle,
  changeMetaDescription,
}: SeoSettingsProps) {
  const uploadContent = useTranslations(
    "shared.forms.upload.seoLabels.seoSettings"
  );

  return (
    <div className="space-y-7">
      <Text textStyle="TS5" className="font-bold text-black">
        {uploadContent("title")}
      </Text>
<<<<<<< HEAD
      <div>
        <label htmlFor="title" className="text-gray ">
          {uploadContent("metaTitle")}
        </label>
        <Input
          value={metaTitle || ""}
          onChange={(e) => changeMetaTitle(e.target.value)}
          placeholder={uploadContent("metaTitle")}
        />
      </div>
      <div>
        <label htmlFor="description" className="text-gray ">
          {uploadContent("metaDescription")}
        </label>
        <Textarea
          value={metaDescription || ""}
          onChange={(e) => changeMetaDescription(e.target.value)}
          placeholder={uploadContent("metaDescription")}
          className="border-[2px] border-lightGray h-[100px]"
        />
=======

      <div className="w-full">
        <div
          style={{
            display: activeLanguage === "arabic" ? "block" : "none",
          }}
        >
          <MultilanguageMetaFields
            multilanguage={true}
            activeLanguage={"arabic"}
            metaTitle={metaTitle}
            metaDescription={metaDescription}
            onTitleChange={changeMetaTitle}
            onDescriptionChange={changeMetaDescription}
          />
        </div>
        <div
          style={{
            display: activeLanguage === "french" ? "block" : "none",
          }}
        >
          <MultilanguageMetaFields
            multilanguage={true}
            activeLanguage={"french"}
            metaTitle={metaTitle}
            metaDescription={metaDescription}
            onTitleChange={changeMetaTitle}
            onDescriptionChange={changeMetaDescription}
          />
        </div>

        <div
          style={{
            display: activeLanguage === "english" ? "block" : "none",
          }}
        >
          <MultilanguageMetaFields
            multilanguage={true}
            activeLanguage={"english"}
            metaTitle={metaTitle}
            metaDescription={metaDescription}
            onTitleChange={changeMetaTitle}
            onDescriptionChange={changeMetaDescription}
          />
        </div>
>>>>>>> 1a01b64fcd4886e9063b75aa7f3a125119f2a87c
      </div>
    </div>
  );
}
