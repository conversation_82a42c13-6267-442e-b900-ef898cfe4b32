"use client";
import WarnInput from "@/components/input/warn-input";
import Text from "@/styles/text-styles";
import ImageUpload from "@/components/images-management/image-upload";
import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";
import { Label } from "@/components/ui/label";
import TextEditor from "@/components/text-editor";
import useSeoMetaData from "@/modules/seo/hooks/use-seo-meta-data";
import { usePreviousUrl } from "@/hooks/urls-management/use-previous-url";
import useBLogUpload from "../../hooks/use-blog-upload";
import FormSubmission from "@/components/form-submission";

import KeywordsSection from "@/modules/seo/components/seo-meta-content/keywords";
import SeoMetaContent from "@/modules/seo/components/seo-meta-content/meta-content";

export default function BlogCreation() {
  const t = useTranslations("BlogsManagement");
  const uploadContent = useTranslations("shared.forms.upload");

  const router = useRouter();
  const previousUrl = usePreviousUrl();

  const {
    metaTitle,
    metaDescription,
    keywords,
    handleMetaTitleChange,
    handleMetaDescriptionChange,
    addNewKeyword,
    removeKeyword,
    getMetaContent,
  } = useSeoMetaData();

  const { formRef, submitBlog, warning, isPending } = useBLogUpload(
    getMetaContent()
  );

  const cancelSubmission = () => {
    if (previousUrl && previousUrl.startsWith("/blogs"))
      router.push(previousUrl);
    else router.push("/blogs");
  };

  return (
    <FormSubmission
      submit={uploadContent("save")}
      cancel={uploadContent("cancel")}
      isPending={isPending}
      onCancel={cancelSubmission}
      onSubmit={submitBlog}
    >
      <form ref={formRef} className="flex flex-col extraXL:flex-row gap-4">
        <div className="basis-[70%] regularL:order-1 order-2 flex flex-col gap-5">
          <div className="rounded-2xl border border-lightGray p-7 bg-white space-y-7">
            <Text textStyle="TS4" className="font-bold text-black">
              {t("blogInfo")}
            </Text>

            <div className="text-red self-center">{warning}</div>

            <div className="flex flex-col space-y-5">
              {/* Title */}
              <div className="w-full flex flex-col space-y-2">
                <Label htmlFor="title">
                  {`${uploadContent("blogLabels.title")} ${uploadContent(
                    "required"
                  )}`}
                </Label>
                <WarnInput id="title" name="title" warning="" />
              </div>

              {/* Description */}
              <div className="w-full flex flex-col space-y-2">
                <Label htmlFor="description">
                  {`${uploadContent("blogLabels.description")} ${uploadContent(
                    "required"
                  )}`}
                </Label>
                <TextEditor
                  name="description"
                  placeholder={uploadContent("blogLabels.description")}
                />
              </div>

              {/* Details */}
              <div className="w-full flex flex-col space-y-2">
                <Label htmlFor="details">
                  {`${uploadContent("blogLabels.details")} ${uploadContent(
                    "required"
                  )}`}
                </Label>
                <TextEditor name="details" />
              </div>

              {/* Display Order */}
              <div className="w-full flex flex-col space-y-2">
                <Label htmlFor="displayOrder">
                  {`${uploadContent("blogLabels.displayOrder")} ${uploadContent(
                    "optional"
                  )}`}
                </Label>
                <WarnInput
                  id="displayOrder"
                  name="displayOrder"
                  type="number"
                  warning=""
                  placeholder="10"
                />
              </div>

              {/* Image Upload */}
              <div className="w-full flex flex-col space-y-2">
                <Label>{uploadContent("blogLabels.image")}</Label>
                <ImageUpload name="image" />
              </div>
            </div>
          </div>
        </div>

        {/* Sidebar */}
        <div className="basis-[30%] order-2 space-y-4">
          {/* Keywords */}
          <div className="rounded-2xl border border-lightGray p-7 bg-white space-y-5">
            <KeywordsSection
              keywords={keywords}
              addNewKeyword={addNewKeyword}
              removeKeyword={removeKeyword}
            />
          </div>

          {/* SEO */}
          <div className="rounded-2xl border border-lightGray p-7 bg-white space-y-5">
            <SeoMetaContent
              metaTitle={metaTitle}
              metaDescription={metaDescription}
              changeMetaTitle={handleMetaTitleChange}
              changeMetaDescription={handleMetaDescriptionChange}
            />
          </div>
        </div>
      </form>
    </FormSubmission>
  );
}
