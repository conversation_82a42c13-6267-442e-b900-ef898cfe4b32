"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Card } from "@/components/ui/card";
import { Trash2 } from "lucide-react";
import { useTranslations } from "next-intl";
import Text from "@/styles/text-styles";
import WarnInput from "@/components/input/warn-input";
import { disableScrollOnNumberInput } from "@/utils/number-input";

interface SectionItemProps {
  section: { title: string; description: string; displayOrder: number };
  index: number;
  onUpdate: (section: any) => void;
  onRemove: () => void;
}

export default function SectionItem({
  section,
  index,
  onUpdate,
  onRemove,
}: SectionItemProps) {
  const t = useTranslations("shared.forms.upload");

  const handleChange = (field: string, value: string | number) => {
    onUpdate({ ...section, [field]: value });
  };

  return (
    <Card className="p-4 space-y-4">
      <div className="flex justify-between items-center">
        <Text textStyle="TS6" className="font-medium">
          {t("productLabels.section")} {index + 1}
        </Text>
        <Button
          type="button"
          variant="outline"
          size="icon"
          onClick={onRemove}
          className="text-red-500 hover:text-red-700"
        >
          <Trash2 className="w-4 h-4" />
        </Button>
      </div>

      <div className="grid grid-cols-1 gap-4">
        <div>
          <Label htmlFor={`section-title-${index}`}>
            {t("productLabels.sectionTitle")} {t("required")}
          </Label>
          <Input
            id={`section-title-${index}`}
            name={`sections[${index}].title`}
            value={section.title || ""}
            onChange={(e) => handleChange("title", e.target.value)}
            placeholder={t("productLabels.sectionTitle")}
          />
        </div>

        <div>
          <Label htmlFor={`section-description-${index}`}>
            {t("productLabels.sectionDescription")} {t("required")}
          </Label>
          <Textarea
            id={`section-description-${index}`}
            name={`sections[${index}].description`}
            value={section.description || ""}
            onChange={(e) => handleChange("description", e.target.value)}
            placeholder={t("productLabels.sectionDescription")}
            rows={3}
          />
        </div>

        <div>
          <Label htmlFor={`section-displayOrder-${index}`}>
            {t("productLabels.displayOrder")} {t("optional")}
          </Label>
          <WarnInput
            id={`section-displayOrder-${index}`}
            name={`sections[${index}].displayOrder`}
            type="number"
            value={section.displayOrder || ""}
            onChange={(e) =>
              handleChange("displayOrder", parseInt(e.target.value) || 0)
            }
            onWheel={disableScrollOnNumberInput}
            placeholder={t("productLabels.displayOrder")}
          />
        </div>
      </div>
    </Card>
  );
}
