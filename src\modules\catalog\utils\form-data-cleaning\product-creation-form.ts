export default function cleanProductCreationFormData(
  formData: FormData
): FormData {
  const filteredFormData = new FormData();
  const categoryIds: string[] = [];
  const sections: any[] = [];
  const faqs: any[] = [];
  const content: Array<{
    name: string;
    description: string;
    details: string;
    language: string;
  }> = [];
  const languages = ["arabic", "french", "english"];

  const metaFieldsToExclude = [
    "metaTitle_arabic",
    "metaDescription_arabic",
    "metaTitle_french",
    "metaDescription_french",
    "metaTitle_english",
    "metaDescription_english",
    "keywords",
    "seoContent",
  ];

  formData.forEach((value, key) => {
    const stringValue = value as string;

    if (key === "displayOrder") {
      if (stringValue.trim() !== "") filteredFormData.append(key, value);
    } else if (key === "categoryIds") {
      categoryIds.push(stringValue);
    } else if (key.startsWith("sections[")) {
      const match = key.match(/sections\[(\d+)\]\.(.+)/);
      if (match) {
        const index = parseInt(match[1]);
        const field = match[2];
        if (!sections[index]) {
          sections[index] = { displayOrder: index + 1 };
        }
        sections[index][field] =
          field === "displayOrder"
            ? parseInt(stringValue) || index + 1
            : stringValue;
      }
    } else if (key.startsWith("faqs[")) {
      const match = key.match(/faqs\[(\d+)\]\.(.+)/);
      if (match) {
        const index = parseInt(match[1]);
        const field = match[2];
        if (!faqs[index]) {
          faqs[index] = { displayOrder: index + 1 };
        }
        faqs[index][field] =
          field === "displayOrder"
            ? parseInt(stringValue) || index + 1
            : stringValue;
      }
    } else if (metaFieldsToExclude.includes(key)) {
      // Skip meta fields that are handled by the new metaContent structure
    } else if (
      !key.includes("_arabic") &&
      !key.includes("_french") &&
      !key.includes("_english") &&
      typeof value === "string" &&
      value.trim() !== ""
    ) {
      filteredFormData.append(key, value);
    }
  });

  languages.forEach((lang) => {
    const nameKey = `name_${lang}`;
    const descKey = `description_${lang}`;
    const detailsKey = `details_${lang}`;

    let nameValue = "";
    let descValue = "";
    let detailsValue = "";

    if (formData.has(nameKey)) {
      nameValue = (formData.get(nameKey) as string)?.trim() || "";
    }

    if (formData.has(descKey)) {
      descValue = (formData.get(descKey) as string)?.trim() || "";
    }

    if (formData.has(detailsKey)) {
      detailsValue = (formData.get(detailsKey) as string)?.trim() || "";
    }

    if (nameValue !== "" || descValue !== "" || detailsValue !== "") {
      content.push({
        name: nameValue,
        description: descValue,
        details: detailsValue,
        language: lang.charAt(0).toUpperCase() + lang.slice(1),
      });
    }
  });

  if (content.length > 0) {
    filteredFormData.append("content", JSON.stringify(content));
  }

  categoryIds.forEach((id) => filteredFormData.append("categoryIds", id));

  // Add sections if any exist
  if (sections.length > 0) {
    const validSections = sections.filter(
      (section) => section && section.title && section.description
    );
    if (validSections.length > 0) {
      filteredFormData.append("sections", JSON.stringify(validSections));
    }
  }

  // Add faqs if any exist
  if (faqs.length > 0) {
    const validFaqs = faqs.filter((faq) => faq && faq.question && faq.answer);
    if (validFaqs.length > 0) {
      filteredFormData.append("faqs", JSON.stringify(validFaqs));
    }
  }

  return filteredFormData;
}
