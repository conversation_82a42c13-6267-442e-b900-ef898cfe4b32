"use client";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Card } from "@/components/ui/card";
import { Trash2 } from "lucide-react";
import { useTranslations } from "next-intl";
import Text from "@/styles/text-styles";
import WarnInput from "@/components/input/warn-input";
import { disableScrollOnNumberInput } from "@/utils/number-input";

interface FaqItemProps {
  faq: { question: string; answer: string; displayOrder: number };
  index: number;
  onUpdate: (faq: any) => void;
  onRemove: () => void;
}

export default function FaqItem({
  faq,
  index,
  onUpdate,
  onRemove,
}: FaqItemProps) {
  const t = useTranslations("shared.forms.upload");

  const handleChange = (field: string, value: string | number) => {
    onUpdate({ ...faq, [field]: value });
  };

  return (
    <Card className="p-4 space-y-4">
      <div className="flex justify-between items-center">
        <Text textStyle="TS6" className="font-medium">
          {t("productLabels.faq")} {index + 1}
        </Text>
        <Button
          type="button"
          variant="outline"
          size="icon"
          onClick={onRemove}
          className="text-red-500 hover:text-red-700"
        >
          <Trash2 className="w-4 h-4" />
        </Button>
      </div>

      <div className="grid grid-cols-1 gap-4">
        <div>
          <Label htmlFor={`faq-question-${index}`}>
            {t("productLabels.faqQuestion")} {t("required")}
          </Label>
          <Input
            id={`faq-question-${index}`}
            name={`faqs[${index}].question`}
            value={faq.question || ""}
            onChange={(e) => handleChange("question", e.target.value)}
            placeholder={t("productLabels.faqQuestion")}
          />
        </div>

        <div>
          <Label htmlFor={`faq-answer-${index}`}>
            {t("productLabels.faqAnswer")} {t("required")}
          </Label>
          <Textarea
            id={`faq-answer-${index}`}
            name={`faqs[${index}].answer`}
            value={faq.answer || ""}
            onChange={(e) => handleChange("answer", e.target.value)}
            placeholder={t("productLabels.faqAnswer")}
            rows={3}
          />
        </div>

        <div>
          <Label htmlFor={`faq-displayOrder-${index}`}>
            {t("productLabels.displayOrder")} {t("optional")}
          </Label>
          <WarnInput
            id={`faq-displayOrder-${index}`}
            name={`faqs[${index}].displayOrder`}
            type="number"
            value={faq.displayOrder || ""}
            onChange={(e) =>
              handleChange("displayOrder", parseInt(e.target.value) || 0)
            }
            onWheel={disableScrollOnNumberInput}
            placeholder={t("productLabels.displayOrder")}
          />
        </div>
      </div>
    </Card>
  );
}
