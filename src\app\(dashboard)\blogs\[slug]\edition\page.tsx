<<<<<<< HEAD
import BlogEdition from "@/modules/blogs/components/blogs-upload/edition";
=======
import BlogEdition from "@/modules/blogs/components/edition";
>>>>>>> 1a01b64fcd4886e9063b75aa7f3a125119f2a87c
import { Suspense } from "react";

interface Props {
  params: Promise<{ slug: string }>;
}

export default async function Page({ params }: Props) {
  const { slug } = await params;
  return (
    <Suspense>
      <BlogEdition blogSlug={slug} />
    </Suspense>
  );
}
